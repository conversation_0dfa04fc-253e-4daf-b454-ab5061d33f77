using UnityEngine;
using UnityEditor;
using UnityEditor.AddressableAssets;
using UnityEditor.AddressableAssets.Settings;
using System.IO;
using System.Linq;

namespace CatsPlantkins.Editor
{
    /// <summary>
    /// Simple tool to set up Addressables for any image sequence
    /// </summary>
    public class SimpleAddressablesSetup : EditorWindow
    {
        private string framesPath = "";
        private string groupName = "Tree Animation Frames";
        
        [MenuItem("Cat's Plantkins/Simple Addressables Setup")]
        public static void ShowWindow()
        {
            GetWindow<SimpleAddressablesSetup>("Addressables Setup");
        }
        
        private void OnGUI()
        {
            GUILayout.Label("Simple Addressables Setup", EditorStyles.boldLabel);
            EditorGUILayout.HelpBox("Select a folder with PNG frames and set up Addressables automatically.", MessageType.Info);
            GUILayout.Space(10);
            
            // Path selection
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("Frames Path:", framesPath.Length > 0 ? framesPath : "None selected");
            if (GUILayout.Button("Browse...", GUILayout.Width(80)))
            {
                SelectFramesFolder();
            }
            EditorGUILayout.EndHorizontal();
            
            groupName = EditorGUILayout.TextField("Group Name:", groupName);
            
            GUILayout.Space(10);
            
            // Show frame count if path is selected
            if (!string.IsNullOrEmpty(framesPath))
            {
                int frameCount = CountFrames();
                EditorGUILayout.HelpBox($"Found {frameCount} PNG frames", frameCount > 0 ? MessageType.Info : MessageType.Warning);
                
                if (frameCount > 0)
                {
                    if (GUILayout.Button("Setup Addressables", GUILayout.Height(30)))
                    {
                        SetupAddressables();
                    }
                }
            }
        }
        
        private void SelectFramesFolder()
        {
            string startPath = Path.Combine(Application.dataPath, "Media/Images/ImageSequences");
            string selectedPath = EditorUtility.OpenFolderPanel("Select Image Sequence Folder", startPath, "");
            
            if (!string.IsNullOrEmpty(selectedPath) && selectedPath.StartsWith(Application.dataPath))
            {
                framesPath = "Assets" + selectedPath.Substring(Application.dataPath.Length).Replace('\\', '/');
            }
        }
        
        private int CountFrames()
        {
            if (string.IsNullOrEmpty(framesPath)) return 0;
            
            string fullPath = Path.Combine(Application.dataPath, framesPath.Replace("Assets/", ""));
            if (!Directory.Exists(fullPath)) return 0;
            
            return Directory.GetFiles(fullPath, "*.png", SearchOption.TopDirectoryOnly).Length;
        }
        
        private void SetupAddressables()
        {
            var settings = AddressableAssetSettingsDefaultObject.Settings;
            if (settings == null)
            {
                EditorUtility.DisplayDialog("Error", "Addressables not initialized", "OK");
                return;
            }
            
            // Get or create group
            var group = settings.FindGroup(groupName);
            if (group == null)
            {
                group = settings.CreateGroup(groupName, false, false, true, null);
            }
            
            // Clear existing entries
            var entriesToRemove = group.entries.ToList();
            foreach (var entry in entriesToRemove)
            {
                settings.RemoveAssetEntry(entry.guid);
            }
            
            // Add new entries
            string fullPath = Path.Combine(Application.dataPath, framesPath.Replace("Assets/", ""));
            string[] pngFiles = Directory.GetFiles(fullPath, "*.png", SearchOption.TopDirectoryOnly);
            
            for (int i = 0; i < pngFiles.Length; i++)
            {
                string relativePath = pngFiles[i].Replace(Application.dataPath, "Assets").Replace('\\', '/');
                string guid = AssetDatabase.AssetPathToGUID(relativePath);
                
                if (!string.IsNullOrEmpty(guid))
                {
                    var entry = settings.CreateOrMoveEntry(guid, group, false, false);
                    entry.address = $"tree-frame-{i + 1:D3}";
                    entry.SetLabel("tree-frames", true);
                }
            }
            
            EditorUtility.SetDirty(settings);
            AssetDatabase.SaveAssets();
            
            EditorUtility.DisplayDialog("Success", $"Set up {pngFiles.Length} frames as Addressable assets", "OK");
        }
    }
}
