using UnityEngine;
using UnityEditor;
using System.IO;

namespace CatsPlantkins.Editor
{
    /// <summary>
    /// Tool to automatically set up your animation frames for best performance.
    ///
    /// For 520x520 pixel frames: Use 1024 texture size (this is perfect!)
    /// For larger frames: Use the next size up (512, 1024, 2048, 4096)
    ///
    /// This tool will make your frames work smoothly with animations.
    /// </summary>
    public class ConfigureAnimationFrames : EditorWindow
    {
        private string framesPath = "Assets/Media/Images/ImageSequences/Experimental-Green-Tree";
        private bool configureForSprites = true;
        private bool enableAlphaTransparency = true;
        private int maxTextureSize = 1024; // Optimal for 520x520 frames (next power of 2)
        private TextureImporterCompression compressionType = TextureImporterCompression.Compressed;
        private int compressionQuality = 50;

        [MenuItem("Tools/Cat's Plantkins/Configure Animation Frames")]
        public static void ShowWindow()
        {
            GetWindow<ConfigureAnimationFrames>("Configure Animation Frames");
        }

        private void OnGUI()
        {
            GUILayout.Label("Setup Animation Frames", EditorStyles.boldLabel);
            EditorGUILayout.HelpBox("This tool will optimize your 520x520 frames for smooth animation playback.", MessageType.Info);
            GUILayout.Space(10);

            framesPath = EditorGUILayout.TextField("Frames Folder:", framesPath);

            GUILayout.Space(10);
            GUILayout.Label("Settings (Recommended values already set)", EditorStyles.boldLabel);

            configureForSprites = EditorGUILayout.Toggle("Set up for 2D Animation", configureForSprites);
            enableAlphaTransparency = EditorGUILayout.Toggle("Keep Transparent Backgrounds", enableAlphaTransparency);

            GUILayout.Space(5);
            maxTextureSize = EditorGUILayout.IntPopup("Texture Size (1024 = Perfect for 520x520)", maxTextureSize,
                new string[] { "512 (Too Small)", "1024 (Perfect!)", "2048 (Bigger than needed)", "4096 (Way too big)" },
                new int[] { 512, 1024, 2048, 4096 });

            compressionType = (TextureImporterCompression)EditorGUILayout.EnumPopup("File Size", compressionType);

            if (compressionType == TextureImporterCompression.Compressed)
            {
                compressionQuality = EditorGUILayout.IntSlider("Quality (50 = Good balance)", compressionQuality, 0, 100);
            }

            GUILayout.Space(20);

            if (GUILayout.Button("Configure All Frames", GUILayout.Height(30)))
            {
                ConfigureFrames();
            }

            GUILayout.Space(10);

            if (GUILayout.Button("Validate Current Settings"))
            {
                ValidateSettings();
            }
        }

        private void ConfigureFrames()
        {
            if (!Directory.Exists(framesPath))
            {
                EditorUtility.DisplayDialog("Error", $"Directory not found: {framesPath}", "OK");
                return;
            }

            string[] pngFiles = Directory.GetFiles(framesPath, "*.png", SearchOption.TopDirectoryOnly);

            if (pngFiles.Length == 0)
            {
                EditorUtility.DisplayDialog("Error", "No PNG files found in the specified directory.", "OK");
                return;
            }

            int configuredCount = 0;

            try
            {
                AssetDatabase.StartAssetEditing();

                for (int i = 0; i < pngFiles.Length; i++)
                {
                    string relativePath = pngFiles[i].Replace(Application.dataPath, "Assets").Replace('\\', '/');

                    if (EditorUtility.DisplayCancelableProgressBar("Configuring Frames",
                        $"Processing {Path.GetFileName(relativePath)}", (float)i / pngFiles.Length))
                    {
                        break;
                    }

                    TextureImporter importer = AssetImporter.GetAtPath(relativePath) as TextureImporter;
                    if (importer != null)
                    {
                        // Configure for 2D animation
                        if (configureForSprites)
                        {
                            importer.textureType = TextureImporterType.Sprite;
                            importer.spriteImportMode = SpriteImportMode.Single;
                        }
                        else
                        {
                            importer.textureType = TextureImporterType.Default;
                        }

                        // Enable alpha transparency
                        importer.alphaIsTransparency = enableAlphaTransparency;

                        // Set texture size and compression
                        importer.maxTextureSize = maxTextureSize;
                        importer.textureCompression = compressionType;

                        if (compressionType == TextureImporterCompression.Compressed)
                        {
                            importer.compressionQuality = compressionQuality;
                        }

                        // Optimize for streaming
                        importer.mipmapEnabled = false; // Disable mipmaps for 2D animations
                        importer.isReadable = false; // Optimize memory usage

                        // Set filter mode for crisp pixels or smooth scaling
                        importer.filterMode = FilterMode.Bilinear;

                        // Apply settings
                        EditorUtility.SetDirty(importer);
                        importer.SaveAndReimport();
                        configuredCount++;
                    }
                }
            }
            finally
            {
                AssetDatabase.StopAssetEditing();
                EditorUtility.ClearProgressBar();
            }

            AssetDatabase.Refresh();

            EditorUtility.DisplayDialog("Configuration Complete",
                $"Successfully configured {configuredCount} animation frames.\n\n" +
                "Settings applied:\n" +
                $"- Texture Type: {(configureForSprites ? "Sprite (2D and UI)" : "Default")}\n" +
                $"- Alpha Transparency: {enableAlphaTransparency}\n" +
                $"- Max Texture Size: {maxTextureSize}\n" +
                $"- Compression: {compressionType}\n" +
                $"- Mipmaps: Disabled\n" +
                $"- Read/Write: Disabled", "OK");
        }

        private void ValidateSettings()
        {
            if (!Directory.Exists(framesPath))
            {
                EditorUtility.DisplayDialog("Error", $"Directory not found: {framesPath}", "OK");
                return;
            }

            string[] pngFiles = Directory.GetFiles(framesPath, "*.png", SearchOption.TopDirectoryOnly);
            int correctlyConfigured = 0;
            int totalFiles = pngFiles.Length;

            foreach (string file in pngFiles)
            {
                string relativePath = file.Replace(Application.dataPath, "Assets").Replace('\\', '/');
                TextureImporter importer = AssetImporter.GetAtPath(relativePath) as TextureImporter;

                if (importer != null)
                {
                    bool isCorrect = true;

                    if (configureForSprites && importer.textureType != TextureImporterType.Sprite)
                        isCorrect = false;
                    if (importer.alphaIsTransparency != enableAlphaTransparency)
                        isCorrect = false;
                    if (importer.maxTextureSize != maxTextureSize)
                        isCorrect = false;

                    if (isCorrect)
                        correctlyConfigured++;
                }
            }

            string message = $"Validation Results:\n\n" +
                           $"Total frames: {totalFiles}\n" +
                           $"Correctly configured: {correctlyConfigured}\n" +
                           $"Need configuration: {totalFiles - correctlyConfigured}";

            EditorUtility.DisplayDialog("Validation Results", message, "OK");
        }
    }
}
