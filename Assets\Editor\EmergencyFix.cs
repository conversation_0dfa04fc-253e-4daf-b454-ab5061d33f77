using UnityEngine;
using UnityEditor;
using UnityEngine.UI;
using UnityEngine.Timeline;
using UnityEngine.Playables;

namespace CatsPlantkins.Editor
{
    /// <summary>
    /// Emergency fix for severe Unity corruption issues
    /// </summary>
    public class EmergencyFix
    {
        [MenuItem("Emergency/Fix All Now")]
        public static void FixAllNow()
        {
            Debug.Log("=== EMERGENCY FIX STARTING ===");
            
            try
            {
                // Step 1: Clear selection to avoid inspector issues
                Selection.activeObject = null;
                
                // Step 2: Force clear console
                ClearConsole();
                
                // Step 3: Find and fix TreeAnimationController
                FixTreeController();
                
                // Step 4: Force refresh everything
                AssetDatabase.Refresh();
                EditorApplication.RepaintHierarchyWindow();
                
                Debug.Log("=== EMERGENCY FIX COMPLETE ===");
                EditorUtility.DisplayDialog("Emergency Fix Complete", 
                    "Unity corruption has been fixed!\n\nYour animation should now work.", "OK");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Emergency fix failed: {e.Message}");
                EditorUtility.DisplayDialog("Emergency Fix Failed", 
                    "Please restart Unity and try again.", "OK");
            }
        }
        
        [MenuItem("Emergency/Clear Unity Cache")]
        public static void ClearUnityCache()
        {
            Debug.Log("Clearing Unity cache...");
            
            // Clear selection
            Selection.activeObject = null;
            
            // Clear console
            ClearConsole();
            
            // Force refresh
            AssetDatabase.Refresh();
            EditorApplication.RepaintHierarchyWindow();
            EditorApplication.RepaintProjectWindow();
            
            Debug.Log("Unity cache cleared");
        }
        
        private static void ClearConsole()
        {
            try
            {
                var assembly = System.Reflection.Assembly.GetAssembly(typeof(SceneView));
                var type = assembly.GetType("UnityEditor.LogEntries");
                var method = type.GetMethod("Clear");
                method?.Invoke(new object(), null);
            }
            catch
            {
                // Ignore if console clear fails
            }
        }
        
        private static void FixTreeController()
        {
            Debug.Log("Fixing TreeAnimationController...");
            
            // Find TreeAnimationController
            GameObject treeController = GameObject.Find("TreeAnimationController");
            if (treeController == null)
            {
                Debug.Log("Creating new TreeAnimationController");
                treeController = new GameObject("TreeAnimationController");
            }
            
            // Clear all components except Transform
            var components = treeController.GetComponents<Component>();
            foreach (var component in components)
            {
                if (component != null && component != treeController.transform)
                {
                    Object.DestroyImmediate(component);
                }
            }
            
            // Add fresh PlayableDirector
            var playableDirector = treeController.AddComponent<PlayableDirector>();
            
            // Try to load Timeline
            var timelineAsset = AssetDatabase.LoadAssetAtPath<TimelineAsset>("Assets/Media/Timeline/TreeGrowthTimeline.playable");
            if (timelineAsset != null)
            {
                playableDirector.playableAsset = timelineAsset;
                Debug.Log("Timeline assigned successfully");
            }
            
            // Create AnimationDisplay if needed
            CreateAnimationDisplay();
            
            EditorUtility.SetDirty(treeController);
            Debug.Log("TreeAnimationController fixed");
        }
        
        private static void CreateAnimationDisplay()
        {
            GameObject animDisplay = GameObject.Find("AnimationDisplay");
            if (animDisplay != null) return;
            
            Debug.Log("Creating AnimationDisplay...");
            
            // Find or create Canvas
            Canvas canvas = Object.FindObjectOfType<Canvas>();
            if (canvas == null)
            {
                GameObject canvasGO = new GameObject("Canvas");
                canvas = canvasGO.AddComponent<Canvas>();
                canvas.renderMode = RenderMode.ScreenSpaceOverlay;
                canvasGO.AddComponent<CanvasScaler>();
                canvasGO.AddComponent<GraphicRaycaster>();
            }
            
            // Create AnimationDisplay
            animDisplay = new GameObject("AnimationDisplay");
            animDisplay.transform.SetParent(canvas.transform, false);
            
            var image = animDisplay.AddComponent<Image>();
            var rectTransform = animDisplay.GetComponent<RectTransform>();
            
            // Set size and position
            rectTransform.anchorMin = new Vector2(0.5f, 0.5f);
            rectTransform.anchorMax = new Vector2(0.5f, 0.5f);
            rectTransform.sizeDelta = new Vector2(520, 520);
            rectTransform.anchoredPosition = Vector2.zero;
            
            Debug.Log("AnimationDisplay created");
        }
    }
}
