using UnityEngine;
using UnityEditor;
using UnityEngine.UI;
using UnityEngine.Timeline;
using UnityEngine.Playables;
using UnityEngine.Playables;
using CattishPlantkins.Animation;

namespace CatsPlantkins.Editor
{
    /// <summary>
    /// Simple tool to clean up broken scene references
    /// </summary>
    public class SceneCleanup : EditorWindow
    {
        [MenuItem("Cat's Plantkins/Clean Up Scene")]
        public static void ShowWindow()
        {
            GetWindow<SceneCleanup>("Scene Cleanup");
        }

        private void OnGUI()
        {
            GUILayout.Label("Scene Cleanup Tool", EditorStyles.boldLabel);
            EditorGUILayout.HelpBox("This will help fix broken references in your scene.", MessageType.Info);
            GUILayout.Space(10);

            if (GUILayout.Button("Find Broken Components", GUILayout.Height(30)))
            {
                FindBrokenComponents();
            }

            GUILayout.Space(5);

            if (GUILayout.Button("Fix TreeAnimationController", GUILayout.Height(30)))
            {
                FixTreeAnimationController();
            }

            GUILayout.Space(5);

            if (GUILayout.Button("Check UI Elements", GUILayout.Height(30)))
            {
                CheckUIElements();
            }
        }

        private void FindBrokenComponents()
        {
            GameObject[] allObjects = FindObjectsOfType<GameObject>();
            int brokenCount = 0;

            foreach (GameObject obj in allObjects)
            {
                Component[] components = obj.GetComponents<Component>();
                foreach (Component comp in components)
                {
                    if (comp == null)
                    {
                        Debug.LogError($"Broken component found on: {obj.name}", obj);
                        brokenCount++;
                    }
                }
            }

            if (brokenCount == 0)
            {
                EditorUtility.DisplayDialog("Scan Complete", "No broken components found!", "OK");
            }
            else
            {
                EditorUtility.DisplayDialog("Scan Complete", $"Found {brokenCount} broken components. Check the Console for details.", "OK");
            }
        }

        private void FixTreeAnimationController()
        {
            // Find TreeAnimationController GameObject
            GameObject treeController = GameObject.Find("TreeAnimationController");

            if (treeController == null)
            {
                EditorUtility.DisplayDialog("Not Found", "TreeAnimationController GameObject not found in scene.", "OK");
                return;
            }

            // Force Unity to refresh and clear any cached inspector states
            EditorUtility.SetDirty(treeController);
            AssetDatabase.Refresh();

            // Remove ALL components that might be causing issues
            var allComponents = treeController.GetComponents<Component>();
            foreach (var component in allComponents)
            {
                if (component != treeController.transform) // Don't remove Transform
                {
                    DestroyImmediate(component);
                    Debug.Log($"Removed component: {component.GetType().Name}");
                }
            }

            // Wait a frame for Unity to process the removals
            EditorApplication.delayCall += () => {
                // Add fresh PlayableDirector
                var playableDirector = treeController.AddComponent<PlayableDirector>();
                Debug.Log("Added fresh PlayableDirector component");

                // Load the Timeline asset
                var timelineAsset = AssetDatabase.LoadAssetAtPath<TimelineAsset>("Assets/Media/Timeline/TreeGrowthTimeline.playable");
                if (timelineAsset != null)
                {
                    playableDirector.playableAsset = timelineAsset;
                    Debug.Log("Assigned Timeline asset to PlayableDirector");
                }

                // Add fresh FlexibleTreeAnimationController
                var flexController = treeController.AddComponent<FlexibleTreeAnimationController>();
                Debug.Log("Added fresh FlexibleTreeAnimationController component");

                // Configure the controller for Bismuth-Tree-Growth
                var serializedObject = new SerializedObject(flexController);
                var framesPathProperty = serializedObject.FindProperty("framesPath");
                if (framesPathProperty != null)
                {
                    framesPathProperty.stringValue = "Assets/Media/Images/ImageSequences/Bismuth-Tree-Growth";
                    serializedObject.ApplyModifiedProperties();
                    Debug.Log("Updated frames path to Bismuth-Tree-Growth");
                }

                // Mark everything as dirty and save
                EditorUtility.SetDirty(treeController);
                EditorUtility.SetDirty(flexController);
                AssetDatabase.SaveAssets();

                EditorUtility.DisplayDialog("Fix Complete",
                    "TreeAnimationController has been completely rebuilt with fresh components.\n" +
                    "Configured for Bismuth-Tree-Growth frames.", "OK");
            };
        }

        private void CheckUIElements()
        {
            // Find Canvas
            Canvas canvas = FindObjectOfType<Canvas>();
            if (canvas == null)
            {
                EditorUtility.DisplayDialog("No Canvas", "No Canvas found in scene.", "OK");
                return;
            }

            // Check Button components
            Button[] buttons = canvas.GetComponentsInChildren<Button>();
            int brokenButtons = 0;

            foreach (Button button in buttons)
            {
                if (button.targetGraphic == null)
                {
                    Debug.LogWarning($"Button '{button.name}' has no target graphic", button);
                    brokenButtons++;
                }
            }

            // Check Image components
            Image[] images = canvas.GetComponentsInChildren<Image>();
            int brokenImages = 0;

            foreach (Image image in images)
            {
                if (image == null)
                {
                    brokenImages++;
                }
            }

            string message = $"UI Check Complete:\n";
            message += $"Buttons: {buttons.Length} found, {brokenButtons} with issues\n";
            message += $"Images: {images.Length} found, {brokenImages} broken\n";
            
            if (brokenButtons > 0 || brokenImages > 0)
            {
                message += "\nCheck Console for details.";
            }

            EditorUtility.DisplayDialog("UI Check", message, "OK");
        }

        private void FixTimelineBinding()
        {
            // Find TreeAnimationController GameObject
            GameObject treeController = GameObject.Find("TreeAnimationController");
            if (treeController == null)
            {
                EditorUtility.DisplayDialog("Not Found", "TreeAnimationController GameObject not found in scene.", "OK");
                return;
            }

            var playableDirector = treeController.GetComponent<PlayableDirector>();
            if (playableDirector == null)
            {
                EditorUtility.DisplayDialog("Error", "No PlayableDirector found on TreeAnimationController.", "OK");
                return;
            }

            // Find or create UI Image for animation display
            GameObject animationDisplay = GameObject.Find("AnimationDisplay");
            if (animationDisplay == null)
            {
                // Create Canvas if it doesn't exist
                Canvas canvas = FindObjectOfType<Canvas>();
                if (canvas == null)
                {
                    GameObject canvasGO = new GameObject("Canvas");
                    canvas = canvasGO.AddComponent<Canvas>();
                    canvas.renderMode = RenderMode.ScreenSpaceOverlay;
                    canvasGO.AddComponent<CanvasScaler>();
                    canvasGO.AddComponent<GraphicRaycaster>();
                }

                // Create AnimationDisplay UI Image
                animationDisplay = new GameObject("AnimationDisplay");
                animationDisplay.transform.SetParent(canvas.transform, false);

                var image = animationDisplay.AddComponent<Image>();
                var rectTransform = animationDisplay.GetComponent<RectTransform>();

                // Set up the RectTransform for center display
                rectTransform.anchorMin = new Vector2(0.5f, 0.5f);
                rectTransform.anchorMax = new Vector2(0.5f, 0.5f);
                rectTransform.sizeDelta = new Vector2(520, 520); // Match your frame size
                rectTransform.anchoredPosition = Vector2.zero;

                Debug.Log("Created AnimationDisplay UI Image");
            }

            // Load Timeline asset
            var timelineAsset = AssetDatabase.LoadAssetAtPath<TimelineAsset>("Assets/Media/Timeline/TreeGrowthTimeline.playable");
            if (timelineAsset == null)
            {
                EditorUtility.DisplayDialog("Error", "TreeGrowthTimeline.playable not found!", "OK");
                return;
            }

            playableDirector.playableAsset = timelineAsset;

            // Clear any existing bindings
            foreach (var track in timelineAsset.GetOutputTracks())
            {
                playableDirector.SetGenericBinding(track, null);
            }

            // Set up new binding for Streaming Image Sequence track
            foreach (var track in timelineAsset.GetOutputTracks())
            {
                if (track.name.Contains("Streaming Image Sequence") || track.GetType().Name.Contains("StreamingImageSequence"))
                {
                    playableDirector.SetGenericBinding(track, animationDisplay);
                    Debug.Log($"Bound track '{track.name}' to AnimationDisplay");
                    break;
                }
            }

            EditorUtility.SetDirty(playableDirector);
            EditorUtility.SetDirty(animationDisplay);
            AssetDatabase.SaveAssets();

            EditorUtility.DisplayDialog("Timeline Fix Complete",
                "Timeline binding has been fixed and AnimationDisplay is ready.", "OK");
        }
    }
}
