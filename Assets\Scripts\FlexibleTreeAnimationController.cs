using UnityEngine;
using UnityEngine.Playables;
using UnityEngine.Timeline;
using System.IO;

namespace CattishPlantkins.Animation
{
    /// <summary>
    /// Simple tree animation controller that works with any number of frames
    /// Just handles frame detection and basic playback control
    /// </summary>
    public class FlexibleTreeAnimationController : MonoBehaviour
    {
        [Header("Animation Source")]
        [SerializeField] private string framesPath = "Assets/Media/Images/ImageSequences/Bismuth-Tree-Growth";
        [SerializeField] private float frameRate = 30f;
        
        [Header("Components")]
        [SerializeField] private PlayableDirector playableDirector;
        [SerializeField] private TimelineAsset timelineAsset;
        
        [Header("Runtime Info")]
        [SerializeField, ReadOnly] private int detectedFrameCount = 0;
        [SerializeField, ReadOnly] private float calculatedDuration = 0f;
        
        private void Awake()
        {
            if (playableDirector == null)
                playableDirector = GetComponent<PlayableDirector>();

            DetectFrameCount();

            if (timelineAsset != null)
                playableDirector.playableAsset = timelineAsset;
        }
        
        private void DetectFrameCount()
        {
            string fullPath = Path.Combine(Application.dataPath, framesPath.Replace("Assets/", ""));
            
            if (Directory.Exists(fullPath))
            {
                string[] pngFiles = Directory.GetFiles(fullPath, "*.png", SearchOption.TopDirectoryOnly);
                detectedFrameCount = pngFiles.Length;
                calculatedDuration = detectedFrameCount / frameRate;
                
                Debug.Log($"Detected {detectedFrameCount} frames in {framesPath}, duration: {calculatedDuration:F2}s");
            }
            else
            {
                Debug.LogError($"Frames path not found: {framesPath}");
            }
        }
        

        
        /// <summary>
        /// Set animation to specific progress (0-1)
        /// </summary>
        public void SetProgress(float progress)
        {
            if (playableDirector == null) return;
            
            progress = Mathf.Clamp01(progress);
            playableDirector.time = progress * calculatedDuration;
            playableDirector.Evaluate();
        }
        
        /// <summary>
        /// Play the animation
        /// </summary>
        public void Play()
        {
            if (playableDirector != null)
                playableDirector.Play();
        }
        
        /// <summary>
        /// Stop and reset the animation
        /// </summary>
        public void Stop()
        {
            if (playableDirector != null)
            {
                playableDirector.Stop();
                playableDirector.time = 0;
            }
        }
        
        /// <summary>
        /// Change the frames path and reconfigure
        /// </summary>
        public void SetFramesPath(string newPath)
        {
            framesPath = newPath;
            DetectFrameCount();
        }
        
        /// <summary>
        /// Get current animation progress (0-1)
        /// </summary>
        public float GetProgress()
        {
            if (playableDirector == null || calculatedDuration <= 0) return 0f;
            return (float)(playableDirector.time / calculatedDuration);
        }
    }
    
    /// <summary>
    /// ReadOnly attribute for inspector display
    /// </summary>
    public class ReadOnlyAttribute : PropertyAttribute { }
}

#if UNITY_EDITOR
namespace CattishPlantkins.Animation.Editor
{
    using UnityEditor;
    
    [CustomPropertyDrawer(typeof(ReadOnlyAttribute))]
    public class ReadOnlyDrawer : PropertyDrawer
    {
        public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
        {
            GUI.enabled = false;
            EditorGUI.PropertyField(position, property, label, true);
            GUI.enabled = true;
        }
    }
    
    [CustomEditor(typeof(FlexibleTreeAnimationController))]
    public class FlexibleTreeAnimationControllerEditor : Editor
    {
        public override void OnInspectorGUI()
        {
            DrawDefaultInspector();
            
            GUILayout.Space(10);
            
            var controller = (FlexibleTreeAnimationController)target;
            
            if (GUILayout.Button("Browse for Frames Folder"))
            {
                SerializedProperty pathProp = serializedObject.FindProperty("framesPath");
                string currentPath = pathProp.stringValue;
                string startPath = string.IsNullOrEmpty(currentPath) ?
                    Path.Combine(Application.dataPath, "Media/Images/ImageSequences") :
                    Path.GetDirectoryName(currentPath);

                string selectedPath = EditorUtility.OpenFolderPanel("Select Image Sequence Folder", startPath, "");

                if (!string.IsNullOrEmpty(selectedPath) && selectedPath.StartsWith(Application.dataPath))
                {
                    string relativePath = "Assets" + selectedPath.Substring(Application.dataPath.Length).Replace('\\', '/');

                    pathProp.stringValue = relativePath;
                    serializedObject.ApplyModifiedProperties();

                    controller.SetFramesPath(relativePath);
                }
            }
            
            GUILayout.Space(5);
            
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("Play"))
            {
                controller.Play();
            }
            if (GUILayout.Button("Stop"))
            {
                controller.Stop();
            }
            EditorGUILayout.EndHorizontal();
            
            GUILayout.Space(5);
            
            EditorGUILayout.LabelField("Progress Control:");
            float progress = EditorGUILayout.Slider(controller.GetProgress(), 0f, 1f);
            if (GUI.changed)
            {
                controller.SetProgress(progress);
            }
        }
    }
}
#endif
