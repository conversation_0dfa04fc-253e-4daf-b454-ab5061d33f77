using UnityEngine;
using UnityEditor;
using UnityEngine.UI;
using UnityEngine.Playables;
using UnityEngine.Timeline;
using CattishPlantkins.Animation;

namespace CatsPlantkins.Editor
{
    /// <summary>
    /// Comprehensive tool to fix Unity animation issues and null reference exceptions
    /// </summary>
    public class UnityAnimationFixer : EditorWindow
    {
        [MenuItem("Cat's Plantkins/Unity Animation Fixer")]
        public static void ShowWindow()
        {
            GetWindow<UnityAnimationFixer>("Animation Fixer");
        }

        private void OnGUI()
        {
            GUILayout.Label("Unity Animation Fixer", EditorStyles.boldLabel);
            GUILayout.Space(10);

            GUILayout.Label("This tool fixes common Unity animation issues:", EditorStyles.helpBox);
            GUILayout.Label("• Null reference exceptions in GameObjectInspector");
            GUILayout.Label("• Missing Timeline bindings");
            GUILayout.Label("• Broken Streaming Image Sequence setup");
            GUILayout.Label("• Corrupted UI components");

            GUILayout.Space(20);

            if (GUILayout.Button("1. Fix All Animation Issues", GUILayout.Height(40)))
            {
                FixAllAnimationIssues();
            }

            GUILayout.Space(10);

            if (GUILayout.Button("2. Rebuild TreeAnimationController"))
            {
                RebuildTreeAnimationController();
            }

            GUILayout.Space(10);

            if (GUILayout.Button("3. Fix Timeline Bindings"))
            {
                FixTimelineBindings();
            }

            GUILayout.Space(10);

            if (GUILayout.Button("4. Clear Unity Editor Cache"))
            {
                ClearUnityEditorCache();
            }

            GUILayout.Space(20);
            GUILayout.Label("Current Status:", EditorStyles.boldLabel);
            
            // Show current setup status
            GameObject treeController = GameObject.Find("TreeAnimationController");
            if (treeController != null)
            {
                GUILayout.Label("✓ TreeAnimationController found", EditorStyles.helpBox);
                
                var playableDirector = treeController.GetComponent<PlayableDirector>();
                if (playableDirector != null && playableDirector.playableAsset != null)
                {
                    GUILayout.Label("✓ PlayableDirector with Timeline", EditorStyles.helpBox);
                }
                else
                {
                    GUILayout.Label("✗ PlayableDirector missing or no Timeline", EditorStyles.helpBox);
                }
            }
            else
            {
                GUILayout.Label("✗ TreeAnimationController not found", EditorStyles.helpBox);
            }

            GameObject animDisplay = GameObject.Find("AnimationDisplay");
            if (animDisplay != null)
            {
                GUILayout.Label("✓ AnimationDisplay found", EditorStyles.helpBox);
            }
            else
            {
                GUILayout.Label("✗ AnimationDisplay not found", EditorStyles.helpBox);
            }
        }

        private void FixAllAnimationIssues()
        {
            Debug.Log("=== Starting Complete Animation Fix ===");
            
            // Step 1: Clear Unity cache and refresh
            ClearUnityEditorCache();
            
            // Step 2: Rebuild the animation controller
            RebuildTreeAnimationController();
            
            // Step 3: Fix Timeline bindings
            FixTimelineBindings();
            
            // Step 4: Force Unity to refresh everything
            AssetDatabase.Refresh();
            EditorApplication.RepaintHierarchyWindow();
            
            Debug.Log("=== Animation Fix Complete ===");
            EditorUtility.DisplayDialog("Fix Complete", 
                "All animation issues have been fixed!\n\n" +
                "Your Bismuth-Tree-Growth animation should now work properly.\n" +
                "Try playing the Timeline to test it.", "OK");
        }

        private void RebuildTreeAnimationController()
        {
            Debug.Log("Rebuilding TreeAnimationController...");
            
            // Find or create TreeAnimationController
            GameObject treeController = GameObject.Find("TreeAnimationController");
            if (treeController == null)
            {
                treeController = new GameObject("TreeAnimationController");
                Debug.Log("Created new TreeAnimationController GameObject");
            }

            // Force clear any cached inspector states
            EditorUtility.SetDirty(treeController);
            
            // Remove all components except Transform to start fresh
            var components = treeController.GetComponents<Component>();
            foreach (var component in components)
            {
                if (component != treeController.transform)
                {
                    DestroyImmediate(component);
                }
            }

            // Add fresh PlayableDirector
            var playableDirector = treeController.AddComponent<PlayableDirector>();
            
            // Load and assign Timeline
            var timelineAsset = AssetDatabase.LoadAssetAtPath<TimelineAsset>("Assets/Media/Timeline/TreeGrowthTimeline.playable");
            if (timelineAsset != null)
            {
                playableDirector.playableAsset = timelineAsset;
                Debug.Log("Assigned TreeGrowthTimeline to PlayableDirector");
            }
            else
            {
                Debug.LogWarning("TreeGrowthTimeline.playable not found!");
            }

            // Add FlexibleTreeAnimationController
            var flexController = treeController.AddComponent<FlexibleTreeAnimationController>();
            
            // Configure for Bismuth-Tree-Growth frames
            var serializedObject = new SerializedObject(flexController);
            var framesPathProperty = serializedObject.FindProperty("framesPath");
            if (framesPathProperty != null)
            {
                framesPathProperty.stringValue = "Assets/Media/Images/ImageSequences/Bismuth-Tree-Growth";
                serializedObject.ApplyModifiedProperties();
                Debug.Log("Configured for Bismuth-Tree-Growth frames");
            }

            EditorUtility.SetDirty(treeController);
            EditorUtility.SetDirty(flexController);
            
            Debug.Log("TreeAnimationController rebuilt successfully");
        }

        private void FixTimelineBindings()
        {
            Debug.Log("Fixing Timeline bindings...");
            
            GameObject treeController = GameObject.Find("TreeAnimationController");
            if (treeController == null)
            {
                Debug.LogError("TreeAnimationController not found!");
                return;
            }

            var playableDirector = treeController.GetComponent<PlayableDirector>();
            if (playableDirector == null)
            {
                Debug.LogError("PlayableDirector not found!");
                return;
            }

            // Find or create AnimationDisplay
            GameObject animationDisplay = GameObject.Find("AnimationDisplay");
            if (animationDisplay == null)
            {
                animationDisplay = CreateAnimationDisplay();
            }

            // Ensure AnimationDisplay has required components
            var image = animationDisplay.GetComponent<Image>();
            if (image == null)
            {
                image = animationDisplay.AddComponent<Image>();
            }

            // Load Timeline asset
            var timelineAsset = playableDirector.playableAsset as TimelineAsset;
            if (timelineAsset == null)
            {
                Debug.LogError("No Timeline asset assigned to PlayableDirector!");
                return;
            }

            // Clear existing bindings
            foreach (var track in timelineAsset.GetOutputTracks())
            {
                playableDirector.SetGenericBinding(track, null);
            }

            // Bind Streaming Image Sequence track to AnimationDisplay
            foreach (var track in timelineAsset.GetOutputTracks())
            {
                // Check if this is a Streaming Image Sequence track
                if (track.GetType().Name.Contains("StreamingImageSequence") || 
                    track.name.Contains("Streaming Image Sequence"))
                {
                    playableDirector.SetGenericBinding(track, animationDisplay);
                    Debug.Log($"Bound track '{track.name}' to AnimationDisplay");
                    break;
                }
            }

            EditorUtility.SetDirty(playableDirector);
            EditorUtility.SetDirty(animationDisplay);
            
            Debug.Log("Timeline bindings fixed successfully");
        }

        private GameObject CreateAnimationDisplay()
        {
            Debug.Log("Creating AnimationDisplay UI...");
            
            // Find or create Canvas
            Canvas canvas = FindObjectOfType<Canvas>();
            if (canvas == null)
            {
                GameObject canvasGO = new GameObject("Canvas");
                canvas = canvasGO.AddComponent<Canvas>();
                canvas.renderMode = RenderMode.ScreenSpaceOverlay;
                canvasGO.AddComponent<CanvasScaler>();
                canvasGO.AddComponent<GraphicRaycaster>();
                Debug.Log("Created Canvas");
            }

            // Create AnimationDisplay
            GameObject animationDisplay = new GameObject("AnimationDisplay");
            animationDisplay.transform.SetParent(canvas.transform, false);
            
            var image = animationDisplay.AddComponent<Image>();
            var rectTransform = animationDisplay.GetComponent<RectTransform>();
            
            // Configure RectTransform for center display
            rectTransform.anchorMin = new Vector2(0.5f, 0.5f);
            rectTransform.anchorMax = new Vector2(0.5f, 0.5f);
            rectTransform.sizeDelta = new Vector2(520, 520); // Match frame size
            rectTransform.anchoredPosition = Vector2.zero;

            Debug.Log("Created AnimationDisplay UI Image");
            return animationDisplay;
        }

        private void ClearUnityEditorCache()
        {
            Debug.Log("Clearing Unity Editor cache...");
            
            // Clear console
            var assembly = System.Reflection.Assembly.GetAssembly(typeof(SceneView));
            var type = assembly.GetType("UnityEditor.LogEntries");
            var method = type.GetMethod("Clear");
            method.Invoke(new object(), null);
            
            // Force refresh assets
            AssetDatabase.Refresh();
            
            // Clear selection to avoid inspector issues
            Selection.activeObject = null;
            
            // Force repaint all windows
            EditorApplication.RepaintHierarchyWindow();
            EditorApplication.RepaintProjectWindow();
            
            Debug.Log("Unity Editor cache cleared");
        }
    }
}
