# Cat's Plantkins (Working Title) - Product Requirements Document

## 1\. Executive Summary

Cat's Plantkins (Working Title) is a Unity-based productivity application that combines focus time sessions with virtual plant growing. The core idea is *visual timeboxing*, where users engage in session-based timed focus intervals linked to specific projects (represented as biomes). During each session, a plant/tree grows incrementally, providing real-time visual feedback and reinforcing productivity through symbolic cultivation in a mindful and aesthetically rewarding way. The application targets WebGL and iOS platforms, with offline capabilities for iOS.

The app's unique selling proposition (USP) lies in:
- **Mindfulness via aesthetics:** Offering visual rewards that encourage curiosity and imagination, rather than requiring monetary spend or imposing app-centric tasks. The user defines their own success benchmarks.
- **Motivation through curiosity:** The gradual growth of a unique plant for each focus session aims to keep users engaged and curious to see the final, fully-grown "Plantkin."
- **Fail-safety:** The experience is designed to be pressure-free. Users cannot "lose," and plants do not die if sessions are missed, preventing frustration and supporting sustained productivity.

## 2\. Problem Statement

Many productivity tools lack engaging visual feedback that motivates users to maintain focus. Traditional pomodoro timers provide time management but often fail to create an emotional connection or a visual aesthetics-based sense of achievement. Users frequently abandon these tools due to a lack of engagement, overwhelming gamification, or pressure from a "fail-state" (e.g., dying plants, broken streaks). Cat's Plantkins aims to address this by providing a gentle, rewarding, and visually rich experience centered on user-defined productivity.

## 3\. Target Audience

- Productivity-focused individuals who enjoy gamification
- Students and professionals who use pomodoro technique
- People who enjoy plant-growing or gardening simulations
- Users looking for a calming, visually appealing focus tool
- Age range: 16-45

## 4\. Product Vision

Cat's Plantkins (Working Title) transforms focus time into a rewarding experience by growing virtual "Plantkins" (artistically styled trees/plants) as users complete focus sessions. The application will provide a calming, visually appealing environment with different biomes and Plantkin types, creating a virtual garden that represents the user's productivity journey. The core philosophy is Productivity > Aesthetics > Ludic (Playful) Elements.

The visual style will be guided by a dedicated "Universo Creativo" (Creative Universe) - a mood board and collection of imagery, flavour text, and vocabulary defining the artistic direction, ensuring a cohesive and unique aesthetic. (Details to be provided in a separate Art & Atmosphere document/mood board).

While the working title includes "Cattish," literal cat motifs are not essential to the core app functionality but may be incorporated as optional "flavour" elements or Easter eggs in later versions. The primary focus is on the Plantkins and their biomes.

## 5\. Key Features

### 5.1 Core Functionality

#### 5.1.1 Focus Timer
- Customizable focus sessions (default: 25 minutes).
- Two primary focus modes:
    - **Deep Focus Mode:** Designed for maximum concentration. Locks the user into the app; no switching or background use (behavior may vary by platform, e.g., session pauses if app loses focus).
    - **Flexible Focus Mode:** Allows background activity; the session continues unless the app is explicitly closed by the user.
- Start, pause, and reset functionality for both modes.
- Visual and text countdown display.
- Session completion notification.

#### 5.1.2 Plant Growth System
- Plants grow in real-time as focus session progresses.
- Multiple growth stages (seedling to fully grown).
- Plants, once planted, cannot die; they simply do not grow if no focus session is active for them. This ensures a pressure-free experience.
- Video-based animation with 250+ frames for smooth transitions.
- Plants persist between sessions in user's virtual garden.

#### 5.1.3 Biome System
- Different environments for plants to grow in, representing user-defined projects or focus areas.
- Each biome has a unique visual style and plant types. May include short descriptions or flavour text.
- Initial biome: "Dark Witch Forest."
- Additional biomes unlockable through completed sessions.

#### 5.1.4 Save System
- JSON-based persistence of user data and plant collection.
- Tracks total focus time, completed sessions, and unlocked biomes.
- Stores individual plant data (biome type, growth stage, focus time).

#### 5.1.5 Statistics & Tracking
- Counter for each project (biome) displaying total focus time (minutes/hours/days) dedicated.
- Overall counter for total focus time accumulated across all projects.
- These statistics are for user insight and potential future "awardification" (non-pressuring achievements based on accumulated effort).

### 5.2 Visual and UX Features

#### 5.2.1 Video-Based Plant Animation
- Smooth growth animation using video playback.
- Support for high-quality video assets.
- Frame interpolation using RIFE AI technology.

#### 5.2.2 Parallax Effect
- Subtle background movement for immersive experience.
- Configurable sway speed and amount.
- Ensures background stays visible on screen.

#### 5.2.3 User Interface
- Clean, minimalist design.
- World map for biome selection.
- Focus session screen with timer and controls.
- Plant collection/garden view.

## 6\. Technical Requirements

### 6.1 Platform Support

- Primary: WebGL for browser-based access
- Secondary: iOS with offline capabilities
- Unity 2023.x LTS or newer

### 6.2 Dependencies

- Addressables package for resource management
- Unity UI system
- Universal Render Pipeline (URP)
- TextMeshPro for text rendering

### 6.3 Performance Requirements

- Smooth animation playback (30+ FPS)
- Efficient video rendering
- Low memory footprint for mobile devices
- Quick loading times for WebGL version

## 7\. User Flows

### 7.1 First-Time User Experience

1. User opens application
2. Boot scene initializes Addressables
3. User is taken to Home/World Map screen
4. Tutorial highlights available biome (Dark Witch Forest)
5. User selects biome and starts first focus session
6. Timer begins and plant starts growing from seed
7. Upon completion, plant is added to user's collection

### 7.2 Returning User Experience

1. User opens application
2. Boot scene loads saved data
3. User is taken to Home/World Map screen showing progress
4. User can view previously grown plants
5. User selects biome and starts new focus session
6. New plant grows during session
7. Upon completion, plant is added to collection

### 7.3 Focus Session Flow

1. User selects biome and starts session
2. Timer begins countdown from 25 minutes (or custom duration)
3. Plant grows progressively as timer advances
4. User can pause/resume or reset if needed
5. Upon completion, session is recorded and plant is saved
6. User is shown completion screen with session stats
7. User can return to world map or start new session

## 8\. Implementation Details

### 8.1 Scene Structure

- Boot scene: Initializes game systems and Addressables
- Home/World Map scene: Biome selection and user progress
- Focus Session scene: Timer and plant growth visualization

### 8.2 Core Components

- SessionManager: Controls focus session flow
- SessionTimer: Handles time tracking and events
- BiomeLoader: Loads biome assets via Addressables
- VideoTreeAnimator: Controls video-based plant animation
- SaveService: Handles data persistence
- ParallaxLayer: Creates subtle background movement

### 8.3 Data Structure

- UserData: Stores user progress and unlocked biomes
- PlantData: Stores individual plant information
- SaveData: Container for all persistent data

## 9\. Future Enhancements

### 9.1 Short-term (v1.1)

- Additional biomes (Forest, Desert, Underwater) with unique Plantkins and flavour text.
- Customizable timer durations.
- Sound effects and ambient audio, guided by the "Universo Creativo."
- Further refinement and prominent display of statistics (total/project focus time).

### 9.2 Medium-term (v1.5)

- Optional, non-intrusive "Streak Counter": Tracks consecutive days a focus session is completed. This should be presented subtly and without penalty for breaking a streak.
- More efficient artist workflow support.
- Dark/light theme support.
- Achievements and milestones: These will be non-pressuring and focused on positive reinforcement (e.g., based on total time focused, number of Plantkins fully grown, number of biomes unlocked). Avoid daily challenges or rewards tied to specific login frequencies.
- Potential for expanded flavour text or short narratives upon a Plantkin reaching its final majestic form for the first time.

### 9.3 Long-term (v2.0)

- Mobile notifications (e.g., gentle reminders if a planned session is approaching, user-configurable and opt-in).
- Widget for home screen (iOS).
- More biomes and Plantkin types, continuously expanding the "Universo Creativo."
- Consideration for incorporating optional, non-essential "flavour" elements (e.g., cats, swogs - hybrid dragon frogs, sloths, swords as decorative motifs or Easter eggs) that enhance the vibe without altering core mechanics.
- *Low Priority/Post v2.0 & Conditional:* Plant care mechanics (e.g., watering, fertilizing). This would only be considered if it can be implemented in a way that strictly adheres to the core non-pressure, non-obligation philosophy and adds clear value without becoming a chore or a source of anxiety.

## 10\. Success Metrics

- Personal User Rating ("Vibe-Rating") Score.
- Average session duration.
- Number of grown trees per time interval per user.
- User satisfaction ratings (qualitative feedback on enjoyment, motivation, and mindfulness).
- User retention (daily/weekly active users), viewed as an indicator of sustained value rather than enforced engagement.
