# Tree Growth Animation with Streaming Image Sequence

## Overview

This document outlines the implementation of tree growth animations in Cat's Plantkins using Unity's Streaming Image Sequence package and Timeline integration. This approach provides smooth, efficient playback of frame-by-frame animations with minimal performance impact.

## Features

- **RIFE Frame Interpolation**: Generate smooth intermediate frames between keyframes
- **Unity Streaming Image Sequence**: Efficient playback of PNG sequences
- **Timeline Integration**: Control animation timing and playback
- **Configurable Parameters**: Adjustable frame rates, durations, and folder paths
- **Real-time Updates**: Replace PNG files without code changes
- **Memory Efficient**: Streams images from disk rather than loading all into memory
- **Transparent Background Support**: Properly composites tree animations over game backgrounds

## Prerequisites

- Unity 2023.x LTS or newer
- Required packages:
  - Timeline (com.unity.timeline)
  - Streaming Image Sequence (com.unity.streaming-image-sequence)
  - Addressables (com.unity.addressables)

## Installation

### 1. Package Installation

1. Open Unity Package Manager (Window > Package Manager)
2. Install "Streaming Image Sequence" package:
   - Click "+" > "Add package by name"
   - Enter "com.unity.streaming-image-sequence"
   - Click "Add"
3. Ensure Timeline package is installed (usually included by default)
4. Install Addressables package if not already present

### 2. Project Setup

#### Prepare Animation Frames Directory

1. Create a dedicated directory for your animation frames:
   ```
   Assets/TreeGrowthFrames/
   ```

2. Import your 300 prepared transparent PNG frames:
   - Copy your PNG sequence to the `Assets/TreeGrowthFrames/` directory
   - Ensure frames are named sequentially (e.g., `frame001.png`, `frame002.png`, etc.)

3. Configure texture import settings:
   - Select all imported frames in the Project window
   - In the Inspector, set:
     - Texture Type: "Sprite (2D and UI)"
     - Alpha: "Transparency" (for PNG files with transparency)
     - Compression: "Low Quality" (or adjust based on quality/performance needs)
   - Click "Apply"

## Implementation

### Method 1: Using Streaming Image Sequence Track (Recommended)

This method uses Unity's built-in Timeline system with the Streaming Image Sequence package.

#### Step 1: Create a Timeline Asset

1. In the Project window, right-click > Create > Timeline > "TreeGrowthTimeline"
2. Create a new scene or open your existing scene
3. Create an empty GameObject named "TreeAnimationController"
4. Add a Playable Director component to the GameObject
5. Drag the "TreeGrowthTimeline" asset to the Playable Director's Timeline field

#### Step 2: Set Up the Tree GameObject

1. Create or select the GameObject that will display the tree animation
2. Add a Sprite Renderer component if not already present
3. Ensure the Sprite Renderer has appropriate sorting layer and order settings

#### Step 3: Add Streaming Image Sequence Track

1. Open the Timeline window (Window > Sequencing > Timeline)
2. Select the TreeAnimationController GameObject
3. In the Timeline window, click the "+" button and select "Streaming Image Sequence Track"
4. Right-click on the track and select "Add Streaming Image Sequence Playable Asset"
5. Set the duration of the clip to match your desired animation length

#### Step 4: Configure the Track

1. Select the newly created clip in the Timeline
2. In the Inspector, click "Select Folder" and navigate to `Assets/TreeGrowthFrames/`
3. The track will automatically detect and organize all frames in the folder
4. Bind the track to your tree's Sprite Renderer:
   - Click the track binding field (circle icon)
   - Drag your tree GameObject with the Sprite Renderer to this field

#### Step 5: Configure Playback Settings

1. Select the clip in the Timeline
2. In the Inspector, adjust:
   - Frame Rate: Set to desired FPS (e.g., 24 or 30)
   - Image Index Offset: 0 (or adjust if needed)
   - Loop: Enable if you want the animation to loop
   - Hold Frame: Set which frame to hold when the clip ends

### Method 2: Using StreamableTreeAnimator Component

This method uses a custom component that integrates with your existing SessionTimer.

#### Step 1: Add the StreamableTreeAnimator Component

1. Select your tree GameObject
2. Add the StreamableTreeAnimator component (Add Component > Cat's Plantkins > Runtime > StreamableTreeAnimator)
3. Configure the component:
   - Sprite Renderer: Assign the tree's Sprite Renderer
   - Session Timer: Assign your existing SessionTimer
   - Frame Assets Label: "TreeFrames" (or your custom label)
   - Preload Frames: Enable for smoother playback
   - Max Loaded Frames: 30 (adjust based on memory constraints)

#### Step 2: Set Up Addressables for Frames

1. Open the Addressables Groups window (Window > Asset Management > Addressables > Groups)
2. Create a new group named "TreeFrames"
3. Select all frames in `Assets/TreeGrowthFrames/`
4. Right-click and select "Addressables > Mark as Addressable"
5. In the Inspector, set the label to "TreeFrames"
6. Build the Addressables content (Build > New Build > Default Build Script)

## Integration with Session Manager

To integrate the animation with your existing focus session system:

### For Timeline Method

1. Add a reference to the Playable Director in your SessionManager:

```csharp
[SerializeField] private PlayableDirector treeAnimationDirector;

private void UpdateTreeGrowth(float progress)
{
    if (treeAnimationDirector != null)
    {
        // Calculate time based on progress
        double duration = treeAnimationDirector.duration;
        treeAnimationDirector.time = duration * progress;

        // Evaluate the timeline at the current time
        treeAnimationDirector.Evaluate();
    }
}
```

### For StreamableTreeAnimator Method

The StreamableTreeAnimator already listens to the SessionTimer's progress events, so no additional code is needed in the SessionManager.

## Performance Considerations

- **Memory Usage**: The Streaming Image Sequence package loads only the frames needed for playback, reducing memory usage
- **Disk I/O**: Frames are streamed from disk, so place them on an SSD for best performance
- **Frame Rate**: Adjust the frame rate based on performance needs (24fps is usually sufficient)
- **Texture Compression**: Use appropriate compression settings for your frames
- **Frame Count**: 300 frames should work well, but monitor performance on target devices

## Troubleshooting

### Common Issues

- **Blue or Pink Tree**: Check that your PNG files have proper transparency
- **Missing Frames**: Verify frame naming and that all frames are in the correct directory
- **Stuttering Playback**: Try enabling frame preloading or reducing frame rate
- **Memory Issues**: Reduce the "Max Loaded Frames" setting or use more aggressive texture compression

### Debugging

- Enable "Show Debug Info" in the StreamableTreeAnimator component
- Check the Console for any error messages
- Monitor memory usage in the Profiler window

## Testing

1. **Editor Playback**: Test animation in Unity Editor play mode
2. **Frame Updates**: Replace PNG files and verify automatic updates
3. **Performance**: Monitor frame rate and memory usage during playback
4. **Integration Test**: Verify animation progresses correctly with the focus timer

## Future Improvements

- Add support for multiple tree types/biomes
- Implement frame caching for improved performance
- Add transition effects between animation states
- Create editor tools for easier animation setup

## References

- [Streaming Image Sequence Package Documentation](https://docs.unity3d.com/Packages/com.unity.streaming-image-sequence@latest)
- [Unity Timeline Documentation](https://docs.unity3d.com/Manual/TimelineOverview.html)
- [Addressables System Documentation](https://docs.unity3d.com/Manual/com.unity.addressables.html)