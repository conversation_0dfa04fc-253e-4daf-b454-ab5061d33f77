# Cat's Plantkins

A Unity-based focus timer application that grows virtual plants as you focus. Watch your plant grow from a seedling to a fully grown tree as you complete focus sessions.

## Table of Contents
- [Features](#features)
- [Requirements](#requirements)
- [Setup Instructions](#setup-instructions)
  - [Windows Setup](#windows-setup)
  - [macOS Setup](#macos-setup)
- [Using the Video Plant Setup Tool](#using-the-video-plant-setup-tool)
- [Automated Package Installation](#automated-package-installation)
- [Frame Interpolation with RIF<PERSON>](#frame-interpolation-with-rife)
- [Troubleshooting](#troubleshooting)
  - [Addressables Errors](#addressables-errors)
  - [Blue Screen Issue](#blue-screen-issue)
  - [Video Not Playing (Dark Quad Issue)](#video-not-playing-dark-quad-issue)
  - [Background Moving Off-Screen Issue](#background-moving-off-screen-issue)
  - [Missing Components](#missing-components)
    - [Fixing "The referenced script (Unknown) on this Behaviour is missing!" Error](#fixing-the-referenced-script-unknown-on-this-behaviour-is-missing-error)
  - [Manual Fixes](#manual-fixes)
- [Project Structure](#project-structure)
- [Recent Changes](#recent-changes)
- [License](#license)
- [Credits](#credits)

## Features

- **Focus Timer**: Set customizable focus sessions (default: 25 minutes)
- **Plant Growth Animation**: Plants grow in real-time as your focus session progresses
- **Video-Based Animation**: Smooth plant growth using 250+ animation frames
- **Biome System**: Different environments for your plants (currently featuring "Dark Witch Forest")
- **Parallax Effect**: Subtle background movement for a more immersive experience
- **Progress Tracking**: Save your focus sessions and watch your garden grow over time

## Requirements

- Unity 2023.x LTS or newer
- Required packages (automatically installed via our custom tool):
  - Addressables (asset management)
  - Unity UI (user interface)
  - Universal Render Pipeline (URP) (modern rendering)
  - Streaming Image Sequence (video-based animations)
  - Timeline (animation sequencing)
  - IDE support packages (Visual Studio, VS Code)
  - TextMeshPro (built-in for Unity 6+, auto-installed for older versions)

## Setup Instructions

### Windows Setup

1. **Clone the repository**
   ```
   git clone https://github.com/Insektosaurus/cattish-plantkins.git
   ```

2. **Install Git LFS** (if not already installed)
   ```
   winget install GitHub.GitLFS
   ```
   Or download from: https://git-lfs.github.com/

3. **Initialize Git LFS**
   ```
   cd cattish-plantkins
   git lfs install
   git lfs pull
   ```

4. **Open the project in Unity**
   - Launch Unity Hub
   - Click "Add" and select the cloned repository folder
   - Open the project with Unity 2023.x LTS

5. **Install required packages automatically**
   - In Unity, go to **Tools > Install Required Packages**
   - Click **"Install All Missing Packages"** to automatically install all required Unity packages
   - Wait for the installation to complete (the tool will show progress and notify you when done)
   - **That's it!** No need to manually install packages through Package Manager

6. **Set up the video-based plant animation**
   - Go to Tools > Cat's Plantkins > Setup Video Plant
   - Assign your plant growth MP4 file
   - Click "Create Video Plant"

### macOS Setup

1. **Clone the repository**
   ```
   git clone https://github.com/Insektosaurus/cattish-plantkins.git
   ```

2. **Install Git LFS** (if not already installed)
   ```
   brew install git-lfs
   ```
   Or download from: https://git-lfs.github.com/

3. **Initialize Git LFS**
   ```
   cd cattish-plantkins
   git lfs install
   git lfs pull
   ```

4. **Open the project in Unity**
   - Launch Unity Hub
   - Click "Add" and select the cloned repository folder
   - Open the project with Unity 2023.x LTS

5. **Install required packages automatically**
   - In Unity, go to **Tools > Install Required Packages**
   - Click **"Install All Missing Packages"** to automatically install all required Unity packages
   - Wait for the installation to complete (the tool will show progress and notify you when done)
   - **That's it!** No need to manually install packages through Package Manager

6. **Set up the video-based plant animation**
   - Go to Tools > Cat's Plantkins > Setup Video Plant
   - Assign your plant growth MP4 file
   - Click "Create Video Plant"

## Using the Video Plant Setup Tool

The project includes a custom editor tool to easily set up the video-based plant animation:

1. Go to Tools > Cat's Plantkins > Setup Video Plant
2. Assign your MP4 file showing plant growth animation
3. Configure display options:
   - Create Render Texture: Creates a texture for the video to play on
   - Create Quad: Creates a flat surface to display the animation
   - Quad Size: Adjust the size of the display surface
4. Click "Create Video Plant"

> **Important note on Image Size (height(in pixel) x width(in pixel):**
>
> All images used for plant growth animation (including keyframes for RIFE) should be generated in the size **512x512** pixels, or in multiples thereof (e.g., **512x1024**, **1024x2048**, etc.). This ensures compatibility with Unity's texture import settings and optimal performance. Avoid using non-multiple sizes to prevent scaling artifacts or import issues.

This will automatically:
- Create a GameObject with the VideoTreeAnimator
- Set up the VideoPlayer to play your MP4 file
- Configure the "Dark Witch Forest" biome
- Connect everything to the SessionManager

## Automated Package Installation

Cat's Plantkins includes a custom Unity Editor tool that automatically installs all required packages for you. No more manual package management!

### How to Use the Package Installer

1. **Open the tool**: In Unity, go to **Tools > Install Required Packages**
2. **Review packages**: The tool will show you all required packages and their installation status
3. **Install automatically**: Click **"Install All Missing Packages"** to install everything at once
4. **Monitor progress**: Watch the progress bar as packages are installed one by one
5. **Done!**: The tool will notify you when all packages are successfully installed

### What Gets Installed

The tool automatically installs these essential packages:
- **Addressables** - Asset management and loading system
- **Unity UI** - User interface system
- **Universal Render Pipeline** - Modern rendering pipeline
- **Streaming Image Sequence** - Video-based animation support
- **Timeline** - Animation and cutscene system
- **IDE Support** - Visual Studio and VS Code integration
- **TextMeshPro** - Advanced text rendering (automatically included for Unity versions before 6.0)

> **Smart Version Detection**: The tool automatically detects your Unity version and only installs TextMeshPro if needed (Unity 5.x and earlier). For Unity 6+, TextMeshPro is built-in.

### Benefits

- ✅ **No manual work** - Everything installs automatically
- ✅ **Smart version detection** - Only installs packages compatible with your Unity version
- ✅ **Error handling** - The tool handles installation errors gracefully
- ✅ **Progress tracking** - See exactly what's being installed
- ✅ **Verification** - Confirms all packages are properly installed
- ✅ **Project files** - Can also generate .csproj/.sln files for your IDE

## Frame Interpolation with RIFE

The project includes RIFE (Real-Time Intermediate Flow Estimation), a powerful AI-based frame interpolation tool that can generate smooth animations from a small set of keyframes:

### What is RIFE?

RIFE is an AI model that can generate intermediate frames between two images, creating smooth transitions. This is particularly useful for:
- Creating smooth plant growth animations from a few keyframes
- Generating hundreds of frames from just a handful of original images
- Improving animation quality without manually creating every frame

### Using RIFE (Automated Method)

The easiest way to use RIFE is with the automated script:

1. **Prepare Your Images**:
   - Place 4-6 keyframe images in the `Tools/RIFE/input_frames` folder
   - Name them sequentially (e.g., `frame1.jpg`, `frame2.jpg`, etc.)

2. **Run the Animation Generator**:
   ```powershell
   cd Tools/RIFE
   .\generate_animation.ps1
   ```

3. **That's it!** The script will:
   - Download necessary model files (~40MB) if needed
   - Generate 32 intermediate frames between each pair of images
   - Create a unique sequence folder with all frames in order
   - Generate MP4 and MOV videos automatically

### Customizing Your Animation

You can customize the animation by setting environment variables:

```powershell
# Generate more frames for smoother animation (2^6 = 64 frames between each pair)
$env:RIFE_EXP = 6

# Change the framerate of the output videos
$env:FRAMERATE = 30

# Run the script
.\generate_animation.ps1
```

Each time you run the script, it creates a new sequence folder with a unique ID, so you can keep all your different animations.

### Manual Method (Advanced)

If you prefer more control, you can also run the process manually:

1. **Setup**:
   ```powershell
   cd Tools/RIFE
   .\setup_rife.ps1
   ```

2. **Generate Interpolated Frames**:
   ```powershell
   cd ECCV2022-RIFE
   python inference_img.py --img ../input_frames/frame1.jpg ../input_frames/frame2.jpg --exp=5 --model rife_model/train_log --output ../output_frames/1-2
   ```

3. **Organize Frames**:
   ```powershell
   cd ..
   .\organize_frames.ps1
   ```

For detailed instructions, see:
- [RIFE Documentation](Tools/RIFE/readme.rife.md) - Detailed usage instructions
- [Non-Technical Quickstart Guide](readme.non-technical-quickstart.md) - Beginner-friendly guide
- [RIFE Setup Guide](Docs/Setup-Rife.md) - Setup instructions for new contributors

## Troubleshooting

### Addressables Errors

If you're seeing errors related to Addressables, follow these steps:

1. **Install the Addressables Package**:
   - Open the Package Manager (Window > Package Manager)
   - Click the "+" button and select "Add package by name..."
   - Enter "com.unity.addressables" and click "Add"

2. **Set Up Addressables**:
   - Go to Tools > Cat's Plantkins > Biome Setup > Setup Addressables
   - This will create the necessary directories and set up the Dark Witch Forest biome

3. **Build Addressables Content**:
   - Go to Window > Asset Management > Addressables > Groups
   - Click "Build > New Build > Default Build Script"

4. **Set Up Dark Witch Forest Biome**:
   - Go to Tools > Cat's Plantkins > Biome Setup > Setup Dark Witch Forest
   - This will configure the biome with the correct sprites

### Blue Screen Issue

If you're seeing a blue screen when playing the game:

1. **Check the Boot Scene**:
   - Make sure the Boot scene has a GameBootstrap component
   - Ensure the GameBootstrap is set to initialize Addressables

2. **Check the FocusSession Scene**:
   - Make sure the BiomeLoader has a reference to a SpriteRenderer for the background
   - Ensure the Dark Witch Forest biome is properly set up

3. **Check the VideoTreeAnimator**:
   - Make sure the VideoTreeAnimator has a reference to a VideoPlayer component
   - Ensure the VideoPlayer has the correct video assigned

4. **Check Camera Setup**:
   - Make sure the Main Camera is properly set up
   - The camera should be able to see both the background and the video quad

5. **Check Render Pipeline**:
   - Make sure you're using the Universal Render Pipeline
   - Check that all materials are compatible with URP

### Video Not Playing (Dark Quad Issue)

If you're seeing a dark quad instead of the video playing, follow these steps:

1. **Add the VideoPlayerFix Component**:
   - Select the GameObject with the VideoPlayer component
   - Click "Add Component" and search for "VideoPlayerFix"
   - This will automatically configure the VideoPlayer to render to a texture

2. **Manual Fix**:
   - Make sure the VideoPlayer's Render Mode is set to "Render Texture"
   - Create a RenderTexture asset (right-click in Project > Create > Render Texture)
   - Assign the RenderTexture to the VideoPlayer's Target Texture
   - Create a Material that uses the RenderTexture (Shader: Unlit/Texture)
   - Assign the Material to the Renderer component on the same GameObject

3. **Use the Scene Fix Helper**:
   - Add the SceneFixHelper component to any GameObject in the scene
   - Check the "Fix Video Player" option
   - It will automatically find and fix the VideoPlayer configuration

### Background Moving Off-Screen Issue

If the background is moving off-screen, follow these steps:

1. **Add Looping to the ParallaxLayer**:
   - The ParallaxLayer script has been updated to include looping behavior
   - Make sure you're using the latest version of the script

2. **Adjust Parallax Settings**:
   - Select the GameObject with the ParallaxLayer component
   - Reduce the Scroll Speed to a smaller value (e.g., 0.01)
   - Make sure Loop Background is checked

3. **Use the Scene Fix Helper**:
   - Add the SceneFixHelper component to any GameObject in the scene
   - Check the "Fix Parallax Layer" option
   - It will automatically find and fix the ParallaxLayer configuration

### Missing Components

If you see errors about missing components:

1. Go to Tools > Cat's Plantkins > Clean TreeAnimators to disable conflicting components
2. Make sure the "Dark Witch Forest" biome is properly set up
3. Check that the VideoTreeAnimator is properly connected to the SessionManager
4. Check the console for specific error messages

#### Fixing "The referenced script (Unknown) on this Behaviour is missing!" Error

If you see this error in the Home_WorldMap scene:

1. **Fix the WorldMapManager GameObject**:
   - Select the WorldMapManager GameObject in the Hierarchy
   - Remove the Missing Script component by clicking the gear icon (⚙️) and selecting "Remove Component"
   - Add the WorldMapUI script to the GameObject

2. **Set up the UI elements**:
   - Create a Canvas with an EventSystem if one doesn't exist
   - Create a Button named "DarkWitchForestButton" with a TextMeshPro child for the label
   - Create TextMeshPro elements for "TotalFocusTimeText" and "CompletedSessionsText"
   - Create a "StartSessionButton" with a TextMeshPro label

3. **Assign the UI elements to the WorldMapUI component**:
   - Assign the buttons and text elements to the corresponding fields in the WorldMapUI component
   - Add a Biome Button entry with the name "Dark Witch Forest"

4. **Create a SaveService GameObject**:
   - Create an empty GameObject named "SaveService"
   - Add the SaveService component to it

5. **Ensure the Boot scene is properly set up**:
   - Make sure the Boot scene is at index 0 in the build settings
   - Verify that the GameBootstrap component is set to initialize Addressables

6. **Build Addressables content**:
   - Go to Window > Asset Management > Addressables > Groups
   - Click "Build > New Build > Default Build Script"

### Manual Fixes

If the automatic tools don't work, you can try these manual fixes:

1. **Fix BiomeLoader**:
   - Find the BiomeLoader in the scene
   - Assign a SpriteRenderer to the "Background Renderer" field
   - Add a biome with the name "Dark Witch Forest"

2. **Fix VideoTreeAnimator**:
   - Find the VideoTreeAnimator in the scene
   - Assign a VideoPlayer component
   - Set the video to "dark-witch-forest-tree.MP4"

3. **Fix SessionManager**:
   - Find the SessionManager in the scene
   - Make sure it has references to the BiomeLoader, SessionTimer, and VideoTreeAnimator

4. **Quick Fix for Multiple Issues**:
   - Add an empty GameObject to your scene
   - Add the SceneFixHelper component to it
   - Check both "Fix Video Player" and "Fix Parallax Layer" options
   - Play the scene

## Project Structure

- `Assets/Scripts/Runtime/` - Core game scripts
- `Assets/Scripts/Biomes/` - Biome-related scripts
- `Assets/Scripts/UI/` - User interface scripts
- `Assets/Scripts/Editor/` - Editor tools and utilities
- `Assets/RenderTextures/` - Render textures for video playback
- `Assets/Materials/` - Materials for displaying videos
- `Docs/` - Documentation files
- `Tools/RIFE/` - RIFE frame interpolation tool
  - `ECCV2022-RIFE/` - RIFE implementation
  - `input_frames/` - Input images for frame interpolation
  - `output_frames/` - Generated interpolated frames
  - `generate_animation.ps1` - Automated animation generation script
  - `setup_rife.ps1` - Setup script for RIFE
  - `organize_frames.ps1` - Script for organizing frames
  - `readme.rife.md` - Detailed RIFE documentation

## Recent Changes

See the [Progress Report](Docs/Progress-Report.md) for a detailed list of recent changes and improvements to the project.

## License

[Include license information here]

## Credits

- Created by [Your Name/Team]
- Plant growth animations created using Runway AI

## Adding New Art Assets (Backgrounds and Tree Animations)

### Backgrounds
- Background images can be added in different aspect ratios (e.g., 9:16 for mobile).
- For scrollable backgrounds (wider than 9:16), create images that pan left and right.
- Place new background images in `Assets/Media/Backgrounds/` (create this folder if it doesn't exist).
- In Unity, set up the background as a scrollable image so users can pan left/right during gameplay.

### Tree Animations (MP4s)
- New tree growth animations should be provided as `.mp4` files.
- Run the MP4s through the RIFE routine to extract frames and create smooth animations.
- Remove the background from the frames and save as `.png` files with transparency.
- Place the processed PNG frames in `Assets/Media/Trees/<TreeName>/` for use in Unity.

### Notes
- The project supports experimenting with different backgrounds and multiple trees per biome.
- If you need help with the RIFE process or Unity setup, see the guides in the `Docs/` folder or ask for step-by-step help.